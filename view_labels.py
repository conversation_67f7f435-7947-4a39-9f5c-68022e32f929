import os
import cv2
import numpy as np
from pathlib import Path
import time
from datetime import datetime

def analyze_label(label_path, output_file, verbose=True):
    """
    Analyse une image de label et écrit les résultats dans un fichier.

    Args:
        label_path: Chemin vers l'image de label
        output_file: Fichier ouvert pour écrire les résultats
        verbose: Si True, affiche les informations dans la console

    Returns:
        bool: True si l'analyse a réussi, False sinon
    """
    try:
        # Charger l'image
        label = cv2.imread(str(label_path), cv2.IMREAD_GRAYSCALE)
        if label is None:
            error_msg = f"❌ Impossible de charger l'image: {label_path.name}"
            if verbose:
                print(error_msg)
            output_file.write(f"{error_msg}\n")
            output_file.flush()  # Force l'écriture immédiate
            return False

        # Trouver les valeurs uniques
        unique_values = np.unique(label)
        result_msg = f"{label_path.name}: {unique_values.tolist()}"

        # Éc<PERSON>re dans le fichier
        output_file.write(f"{result_msg}\n")
        output_file.flush()  # Force l'écriture immédiate

        if verbose:
            print(f"✅ {label_path.name}: {unique_values.tolist()}")

        return True

    except Exception as e:
        error_msg = f"❌ Erreur lors de l'analyse de {label_path.name}: {str(e)}"
        if verbose:
            print(error_msg)
        output_file.write(f"{error_msg}\n")
        output_file.flush()
        return False

if __name__ == "__main__":
    # Chemin vers le dossier des labels
    labels_dir = Path(r"C:\Users\<USER>\OneDrive - EvidentScientific\Documents\4Corrosion\Dataset\nnUnet\nnUNet_raw\Dataset012_test4labeldifferent\labelsTr_backup")

    # Paramètres
    verbose = True  # Mode verbeux activé par défaut

    # Lister tous les fichiers .png dans le dossier
    label_files = list(labels_dir.glob("*.png"))

    if not label_files:
        print("❌ Aucun fichier .png trouvé dans le dossier des labels")
    else:
        total_files = len(label_files)
        print(f"📁 {total_files} fichiers trouvés")

        # Créer le fichier de sortie avec timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_filename = f"view_labels_results_{timestamp}.txt"
        output_path = labels_dir / output_filename

        print(f"💾 Sauvegarde des résultats dans: {output_path}")
        print(f"🔄 Mode verbeux: {'Activé' if verbose else 'Désactivé'}")
        print("-" * 70)

        start_time = time.time()
        successful_analyses = 0
        failed_analyses = 0

        # Ouvrir le fichier de sortie
        with open(output_path, 'w', encoding='utf-8') as output_file:
            # Écrire l'en-tête
            output_file.write(f"Analyse des labels - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            output_file.write(f"Dossier analysé: {labels_dir}\n")
            output_file.write(f"Nombre total de fichiers: {total_files}\n")
            output_file.write("=" * 70 + "\n\n")

            # Analyser chaque fichier
            for i, label_file in enumerate(label_files, 1):
                if verbose:
                    # Afficher la progression
                    progress_percent = (i / total_files) * 100
                    elapsed_time = time.time() - start_time
                    if i > 1:
                        avg_time_per_file = elapsed_time / (i - 1)
                        estimated_remaining = avg_time_per_file * (total_files - i)
                        eta_str = f" - ETA: {estimated_remaining:.1f}s"
                    else:
                        eta_str = ""

                    print(f"[{i:6d}/{total_files:6d}] ({progress_percent:5.1f}%){eta_str}")

                # Analyser le fichier
                success = analyze_label(label_file, output_file, verbose)

                if success:
                    successful_analyses += 1
                else:
                    failed_analyses += 1

                # Afficher un résumé périodique (tous les 1000 fichiers)
                if verbose and i % 1000 == 0:
                    elapsed = time.time() - start_time
                    print(f"📊 Progression: {i}/{total_files} fichiers traités en {elapsed:.1f}s")
                    print(f"   ✅ Réussis: {successful_analyses}, ❌ Échecs: {failed_analyses}")
                    print("-" * 50)

        # Résumé final
        total_time = time.time() - start_time
        print("\n" + "=" * 70)
        print("📋 RÉSUMÉ FINAL")
        print("=" * 70)
        print(f"📁 Fichiers traités: {total_files}")
        print(f"✅ Analyses réussies: {successful_analyses}")
        print(f"❌ Analyses échouées: {failed_analyses}")
        print(f"⏱️  Temps total: {total_time:.2f} secondes")
        print(f"⚡ Vitesse moyenne: {total_files/total_time:.2f} fichiers/seconde")
        print(f"💾 Résultats sauvegardés dans: {output_path}")
        print("=" * 70)