import os
from pathlib import Path
import re

def normalize_filename(filename):
    """
    Supprime les suffixes spécifiques pour extraire un identifiant commun utilisable pour l’appariement.
    Exemples :
        - PS-00400_MX2-757..._segmentation_mask_slice_classes_0000.png
        - PS-00400_MX2-757..._raw_data_slice_uint8_nde_0000.png
        → PS-00400_MX2-757-SC-PS-00400-Z-46000_0000
    """
    # Supprime le type (mask / raw) et garde l’identifiant + index
    filename = Path(filename).stem
    match = re.match(r"(.*?)(_segmentation_mask_slice_classes|_raw_data_slice_uint8_nde)?_(\d+)$", filename)
    if match:
        return f"{match.group(1)}_{match.group(3)}"
    return None

def rename_matching_files(folder_a, folder_b, prefix_format="{:04d}"):
    folder_a = Path(folder_a)
    folder_b = Path(folder_b)

    files_a = {normalize_filename(f.name): f for f in folder_a.iterdir() if f.is_file()}
    files_b = {normalize_filename(f.name): f for f in folder_b.iterdir() if f.is_file()}

    # Obtenir les fichiers appariés
    common_keys = sorted(set(files_a.keys()) & set(files_b.keys()))

    if not common_keys:
        print("❌ Aucun fichier apparié trouvé.")
        return

    for idx, key in enumerate(common_keys, 1):
        new_name = prefix_format.format(idx) + ".png"
        new_file_a = folder_a / new_name
        new_file_b = folder_b / new_name

        os.rename(files_a[key], new_file_a)
        os.rename(files_b[key], new_file_b)

    print(f"✅ {len(common_keys)} paires renommées avec succès.")

# === Exemple d'utilisation ===
if __name__ == "__main__":
    dossier_a = r"C:\Users\<USER>\Documents\4Corrosion\Dataset\prod_data_dataset\collected_volume_data\masques"
    dossier_b = r"C:\Users\<USER>\Documents\4Corrosion\Dataset\prod_data_dataset\collected_volume_data\uint8"
    rename_matching_files(dossier_a, dossier_b)
