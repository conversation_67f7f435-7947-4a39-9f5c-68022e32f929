import os
import cv2
import numpy as np
from pathlib import Path

def analyze_label(label_path):
    # Charger l'image
    label = cv2.imread(str(label_path), cv2.IMREAD_GRAYSCALE)
    if label is None:
        print(f"[ERREUR] Impossible de charger l'image: {label_path}")
        return

    # Trouver les valeurs uniques
    unique_values = np.unique(label)
    print(f"{label_path.name}: {unique_values}")

if __name__ == "__main__":
    # Chemin vers le dossier des labels
    labels_dir = Path(r"C:/Users/<USER>/Documents/4Corrosion/Dataset/prod_data_dataset/collected_volume_data/masques")
    
    # Lister tous les fichiers .png dans le dossier
    label_files = list(labels_dir.glob("*.png"))
    
    if not label_files:
        print("[ERREUR] Aucun fichier .png trouvé dans le dossier des labels")
    else:
        print(f"[DOSSIER] {len(label_files)} fichiers trouvés\n")
        print("Valeurs uniques dans chaque image :")
        print("-" * 50)
        # Analyser chaque fichier
        for label_file in label_files:
            analyze_label(label_file) 